# Question Pool Frontend Integration

This document describes how the frontend can query question pools and use those questions to add to worksheets.

## Overview

The system now provides API endpoints for the frontend to:
1. Search and filter questions from the question pool
2. Get available filter options
3. Add questions from the pool directly to worksheets

## API Endpoints

### 1. Search Question Pool

**Endpoint:** `GET /question-pool/search`

**Description:** Search and filter questions from the question pool with comprehensive filtering options.

**Authentication:** Required (Bearer token)

**Access Control:**
- **Admin users**: Can search all questions in the pool
- **School Manager**: Can search questions relevant to their school context
- **Teacher**: Can search questions relevant to their school context  
- **Independent Teacher**: Can search all public questions

**Query Parameters:**

| Parameter | Type | Required | Description | Example |
|-----------|------|----------|-------------|---------|
| `subject` | string | No | Subject filter | `Mathematics` |
| `parentSubject` | string | No | Parent subject filter | `Mathematics` |
| `childSubject` | string | No | Child subject filter | `Algebra` |
| `type` | array | No | Question type filter | `multiple_choice,true_false` |
| `grade` | string | No | Grade level filter | `Primary 2` |
| `difficulty` | array | No | Question difficulty filter | `easy,medium` |
| `status` | string | No | Question status filter | `active` (default) |
| `language` | string | No | Language filter | `English` (default) |
| `searchText` | string | No | Search text in question content | `capital of France` |
| `tags` | array | No | Tags filter | `geometry,basic` |
| `optionTypeId` | string | No | Option type ID filter | UUID |
| `optionValueId` | string | No | Option value ID filter | UUID |
| `page` | number | No | Page number for pagination | `1` (default) |
| `limit` | number | No | Items per page (1-100) | `20` (default) |
| `sortBy` | string | No | Sort field | `createdAt` (default) |
| `sortOrder` | string | No | Sort order | `desc` (default) |
| `hasMedia` | boolean | No | Include only questions with media | `false` (default) |
| `excludeRecentlyUsedHours` | number | No | Exclude recently used questions (hours) | `24` |

**Response:**

```json
{
  "questions": [
    {
      "id": "507f1f77bcf86cd799439011",
      "type": "multiple_choice",
      "content": "What is the capital of France?",
      "options": ["Paris", "London", "Berlin", "Madrid"],
      "answer": ["Paris"],
      "explain": "Paris is the capital and largest city of France.",
      "subject": "Geography",
      "parentSubject": "Social Studies",
      "childSubject": "World Geography",
      "grade": "Primary 4",
      "difficulty": "easy",
      "status": "active",
      "language": "English",
      "image": null,
      "imagePrompt": null,
      "tags": ["geography", "capitals"],
      "optionValue": {},
      "createdAt": "2024-01-15T10:30:00Z",
      "updatedAt": "2024-01-15T10:30:00Z"
    }
  ],
  "total": 150,
  "page": 1,
  "limit": 20,
  "totalPages": 8,
  "hasNextPage": true,
  "hasPreviousPage": false
}
```

### 2. Get Filter Options

**Endpoint:** `GET /question-pool/filters`

**Description:** Returns all available filter options for question pool search.

**Authentication:** Required (Bearer token)

**Response:**

```json
{
  "subjects": ["Mathematics", "Science", "English", "Geography"],
  "parentSubjects": ["Mathematics", "Science", "Language Arts"],
  "childSubjects": ["Algebra", "Geometry", "Biology", "Chemistry"],
  "grades": ["Primary 1", "Primary 2", "Primary 3", "Secondary 1"],
  "types": ["multiple_choice", "true_false", "short_answer", "essay"],
  "difficulties": ["very_easy", "easy", "medium", "hard", "very_hard"],
  "languages": ["English", "Chinese", "Malay"],
  "tags": ["basic", "advanced", "geometry", "algebra"],
  "totalQuestions": 1250
}
```

### 3. Get Question by ID

**Endpoint:** `GET /question-pool/:id`

**Description:** Retrieve detailed information about a specific question from the question pool.

**Authentication:** Required (Bearer token)

**Parameters:**
- `id` (path): Question ID

**Response:**

```json
{
  "id": "507f1f77bcf86cd799439011",
  "type": "multiple_choice",
  "content": "What is the capital of France?",
  "options": ["Paris", "London", "Berlin", "Madrid"],
  "answer": ["Paris"],
  "explain": "Paris is the capital and largest city of France.",
  "subject": "Geography",
  "parentSubject": "Social Studies",
  "childSubject": "World Geography",
  "grade": "Primary 4",
  "difficulty": "easy",
  "status": "active",
  "language": "English",
  "image": null,
  "imagePrompt": null,
  "tags": ["geography", "capitals"],
  "optionValue": {},
  "createdAt": "2024-01-15T10:30:00Z",
  "updatedAt": "2024-01-15T10:30:00Z"
}
```

## Adding Pool Questions to Worksheets

### Enhanced AddQuestionToWorksheetDto

The `AddQuestionToWorksheetDto` now supports a new optional field:

```typescript
interface AddQuestionToWorksheetDto {
  // Existing fields...
  type: 'multiple_choice' | 'true_false' | '...';
  content: string;
  options: string[];
  answer: string[];
  explain: string;
  
  // New field for question pool integration
  questionPoolId?: string; // Optional MongoDB ObjectId
  
  // Optional worksheet-specific overrides
  position?: number;
  points?: number;
  optionTypeId?: string;
  optionValueId?: string;
}
```

### Usage Scenarios

#### Scenario 1: Add Question from Pool (Recommended)

When `questionPoolId` is provided, the system will:
1. Fetch the question from the question pool
2. Validate user access to the question
3. Copy all question content (type, content, options, answer, explain, etc.)
4. Apply any worksheet-specific overrides (points, position, etc.)
5. Track usage statistics

**Request:**
```json
POST /worksheets/123/questions
{
  "questionPoolId": "507f1f77bcf86cd799439011",
  "points": 5,
  "position": 1
}
```

#### Scenario 2: Create New Question (Existing Functionality)

When `questionPoolId` is not provided, the system works as before:

**Request:**
```json
POST /worksheets/123/questions
{
  "type": "multiple_choice",
  "content": "What is 2 + 2?",
  "options": ["3", "4", "5", "6"],
  "answer": ["4"],
  "explain": "2 + 2 equals 4",
  "points": 2
}
```

## Frontend Implementation Guide

### 1. Question Pool Search Component

```typescript
// Example React component for question pool search
const QuestionPoolSearch = () => {
  const [questions, setQuestions] = useState([]);
  const [filters, setFilters] = useState({});
  const [loading, setLoading] = useState(false);

  const searchQuestions = async (searchParams) => {
    setLoading(true);
    try {
      const response = await fetch('/api/question-pool/search?' + 
        new URLSearchParams(searchParams), {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });
      const data = await response.json();
      setQuestions(data.questions);
    } catch (error) {
      console.error('Error searching questions:', error);
    } finally {
      setLoading(false);
    }
  };

  // Component JSX...
};
```

### 2. Add Question to Worksheet

```typescript
const addQuestionToWorksheet = async (worksheetId, questionPoolId, options = {}) => {
  try {
    const response = await fetch(`/api/worksheets/${worksheetId}/questions`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        questionPoolId,
        ...options // points, position, etc.
      })
    });
    
    if (response.ok) {
      const addedQuestion = await response.json();
      console.log('Question added successfully:', addedQuestion);
      return addedQuestion;
    } else {
      throw new Error('Failed to add question');
    }
  } catch (error) {
    console.error('Error adding question:', error);
    throw error;
  }
};
```

### 3. Complete Workflow Example

```typescript
// 1. Search for questions
const searchResults = await searchQuestions({
  subject: 'Mathematics',
  grade: 'Primary 2',
  type: ['multiple_choice'],
  limit: 10
});

// 2. User selects a question
const selectedQuestion = searchResults.questions[0];

// 3. Add selected question to worksheet
const addedQuestion = await addQuestionToWorksheet(
  worksheetId, 
  selectedQuestion.id,
  { points: 5, position: 1 }
);
```

## Benefits

1. **Reusability**: Questions from the pool can be reused across multiple worksheets
2. **Consistency**: Standardized questions ensure consistent quality
3. **Efficiency**: No need to recreate similar questions
4. **Analytics**: Track which questions are most popular/effective
5. **Maintenance**: Updates to pool questions can be managed centrally

## Error Handling

Common error responses:

- `400 Bad Request`: Invalid search parameters or question data
- `403 Forbidden`: Insufficient permissions to access questions
- `404 Not Found`: Question not found in pool
- `409 Conflict`: Question limit exceeded in worksheet

## Performance Considerations

- Search results are paginated (max 100 items per page)
- Caching is implemented for frequently accessed questions
- Database indexes optimize common query patterns
- Recently used questions can be excluded to promote diversity
