# Question Pool API Testing Guide

This document provides examples for testing the new question pool API endpoints.

## Prerequisites

1. Ensure the application is running
2. Have a valid authentication token
3. Have some questions in the question pool (you can add them via the existing question generation system)

## Testing with cURL

### 1. Get Available Filter Options

```bash
curl -X GET "http://localhost:3000/question-pool/filters" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

### 2. Search Questions (Basic)

```bash
curl -X GET "http://localhost:3000/question-pool/search?limit=5" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

### 3. Search Questions with Filters

```bash
curl -X GET "http://localhost:3000/question-pool/search?subject=Mathematics&grade=Primary%202&type=multiple_choice&limit=10&page=1" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

### 4. Search Questions with Text Search

```bash
curl -X GET "http://localhost:3000/question-pool/search?searchText=addition&limit=5" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

### 5. Get Specific Question by ID

```bash
curl -X GET "http://localhost:3000/question-pool/QUESTION_ID_HERE" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

### 6. Add Question from Pool to Worksheet

```bash
curl -X POST "http://localhost:3000/worksheets/WORKSHEET_ID_HERE/questions" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "questionPoolId": "QUESTION_POOL_ID_HERE",
    "points": 5,
    "position": 1
  }'
```

### 7. Add Custom Question to Worksheet (Existing Functionality)

```bash
curl -X POST "http://localhost:3000/worksheets/WORKSHEET_ID_HERE/questions" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "type": "multiple_choice",
    "content": "What is 2 + 2?",
    "options": ["3", "4", "5", "6"],
    "answer": ["4"],
    "explain": "2 + 2 equals 4",
    "points": 2
  }'
```

## Testing with Postman

### Collection Setup

1. Create a new Postman collection called "Question Pool API"
2. Add an environment variable for `baseUrl` (e.g., `http://localhost:3000`)
3. Add an environment variable for `authToken` with your JWT token

### Request Examples

#### 1. Get Filter Options
- **Method**: GET
- **URL**: `{{baseUrl}}/question-pool/filters`
- **Headers**: 
  - `Authorization: Bearer {{authToken}}`
  - `Content-Type: application/json`

#### 2. Search Questions
- **Method**: GET
- **URL**: `{{baseUrl}}/question-pool/search`
- **Headers**: 
  - `Authorization: Bearer {{authToken}}`
  - `Content-Type: application/json`
- **Query Params**:
  - `subject`: Mathematics
  - `grade`: Primary 2
  - `type`: multiple_choice
  - `limit`: 10
  - `page`: 1

#### 3. Add Pool Question to Worksheet
- **Method**: POST
- **URL**: `{{baseUrl}}/worksheets/{{worksheetId}}/questions`
- **Headers**: 
  - `Authorization: Bearer {{authToken}}`
  - `Content-Type: application/json`
- **Body** (JSON):
```json
{
  "questionPoolId": "{{questionPoolId}}",
  "points": 5,
  "position": 1
}
```

## Testing with JavaScript/Frontend

### Example Test Script

```javascript
// Test configuration
const API_BASE_URL = 'http://localhost:3000';
const AUTH_TOKEN = 'YOUR_JWT_TOKEN_HERE';

const headers = {
  'Authorization': `Bearer ${AUTH_TOKEN}`,
  'Content-Type': 'application/json'
};

// Test 1: Get filter options
async function testGetFilterOptions() {
  try {
    const response = await fetch(`${API_BASE_URL}/question-pool/filters`, {
      method: 'GET',
      headers
    });
    
    if (response.ok) {
      const data = await response.json();
      console.log('Filter options:', data);
      return data;
    } else {
      console.error('Failed to get filter options:', response.status);
    }
  } catch (error) {
    console.error('Error:', error);
  }
}

// Test 2: Search questions
async function testSearchQuestions() {
  try {
    const params = new URLSearchParams({
      subject: 'Mathematics',
      grade: 'Primary 2',
      limit: '5'
    });
    
    const response = await fetch(`${API_BASE_URL}/question-pool/search?${params}`, {
      method: 'GET',
      headers
    });
    
    if (response.ok) {
      const data = await response.json();
      console.log('Search results:', data);
      return data;
    } else {
      console.error('Failed to search questions:', response.status);
    }
  } catch (error) {
    console.error('Error:', error);
  }
}

// Test 3: Add question from pool to worksheet
async function testAddPoolQuestionToWorksheet(worksheetId, questionPoolId) {
  try {
    const response = await fetch(`${API_BASE_URL}/worksheets/${worksheetId}/questions`, {
      method: 'POST',
      headers,
      body: JSON.stringify({
        questionPoolId: questionPoolId,
        points: 5,
        position: 1
      })
    });
    
    if (response.ok) {
      const data = await response.json();
      console.log('Question added successfully:', data);
      return data;
    } else {
      const errorData = await response.json();
      console.error('Failed to add question:', response.status, errorData);
    }
  } catch (error) {
    console.error('Error:', error);
  }
}

// Run all tests
async function runTests() {
  console.log('Starting Question Pool API tests...');
  
  // Test 1
  console.log('\n1. Testing filter options...');
  await testGetFilterOptions();
  
  // Test 2
  console.log('\n2. Testing question search...');
  const searchResults = await testSearchQuestions();
  
  // Test 3 (requires valid worksheet ID and question from search results)
  if (searchResults && searchResults.questions.length > 0) {
    console.log('\n3. Testing add question to worksheet...');
    const questionId = searchResults.questions[0].id;
    const worksheetId = 'YOUR_WORKSHEET_ID_HERE'; // Replace with actual worksheet ID
    await testAddPoolQuestionToWorksheet(worksheetId, questionId);
  }
  
  console.log('\nTests completed!');
}

// Run the tests
runTests();
```

## Expected Responses

### Successful Filter Options Response
```json
{
  "subjects": ["Mathematics", "Science", "English"],
  "parentSubjects": ["Mathematics", "Science"],
  "childSubjects": ["Algebra", "Geometry", "Biology"],
  "grades": ["Primary 1", "Primary 2", "Secondary 1"],
  "types": ["multiple_choice", "true_false", "short_answer"],
  "difficulties": ["easy", "medium", "hard"],
  "languages": ["English", "Chinese"],
  "tags": ["basic", "advanced"],
  "totalQuestions": 150
}
```

### Successful Search Response
```json
{
  "questions": [
    {
      "id": "507f1f77bcf86cd799439011",
      "type": "multiple_choice",
      "content": "What is 5 + 3?",
      "options": ["6", "7", "8", "9"],
      "answer": ["8"],
      "explain": "5 + 3 = 8",
      "subject": "Mathematics",
      "grade": "Primary 2",
      "createdAt": "2024-01-15T10:30:00Z",
      "updatedAt": "2024-01-15T10:30:00Z"
    }
  ],
  "total": 25,
  "page": 1,
  "limit": 5,
  "totalPages": 5,
  "hasNextPage": true,
  "hasPreviousPage": false
}
```

### Successful Add Question Response
```json
{
  "id": "generated-uuid-here",
  "type": "multiple_choice",
  "content": "What is 5 + 3?",
  "options": ["6", "7", "8", "9"],
  "answer": ["8"],
  "explain": "5 + 3 = 8",
  "position": 1,
  "points": 5,
  "questionPoolId": "507f1f77bcf86cd799439011",
  "sourceType": "question_pool",
  "audit": {
    "createdBy": "user-id",
    "createdAt": "2024-01-15T11:00:00Z",
    "version": 1
  }
}
```

## Common Error Responses

### 400 Bad Request
```json
{
  "statusCode": 400,
  "message": "Invalid search parameters",
  "error": "Bad Request"
}
```

### 403 Forbidden
```json
{
  "statusCode": 403,
  "message": "Insufficient permissions to access questions",
  "error": "Forbidden"
}
```

### 404 Not Found
```json
{
  "statusCode": 404,
  "message": "Question with ID 507f1f77bcf86cd799439011 not found in question pool",
  "error": "Not Found"
}
```

## Troubleshooting

1. **Authentication Issues**: Ensure your JWT token is valid and not expired
2. **Empty Results**: Check if there are questions in the question pool database
3. **Permission Errors**: Verify your user role has appropriate permissions
4. **Invalid IDs**: Ensure worksheet IDs and question pool IDs are valid MongoDB ObjectIds
