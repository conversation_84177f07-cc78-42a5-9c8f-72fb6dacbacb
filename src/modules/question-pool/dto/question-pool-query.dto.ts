import { 
  Is<PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>al, 
  Is<PERSON>num, 
  <PERSON><PERSON><PERSON>ber, 
  IsBoolean,
  Min,
  Max,
  ArrayMaxSize,
  <PERSON>Length,
  Transform
} from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { EQuestionType, EQuestionDifficulty, EQuestionStatus } from '../../../shared/interfaces/exercise-question.interface';

/**
 * DTO for searching questions in the question pool
 */
export class QuestionPoolSearchDto {
  @ApiPropertyOptional({ 
    description: 'Subject filter',
    example: 'Mathematics'
  })
  @IsOptional()
  @IsString()
  @MaxLength(100)
  subject?: string;

  @ApiPropertyOptional({ 
    description: 'Parent subject filter',
    example: 'Mathematics'
  })
  @IsOptional()
  @IsString()
  @MaxLength(100)
  parentSubject?: string;

  @ApiPropertyOptional({ 
    description: 'Child subject filter',
    example: 'Algebra'
  })
  @IsOptional()
  @IsString()
  @Max<PERSON>ength(100)
  childSubject?: string;

  @ApiPropertyOptional({ 
    description: 'Question type filter',
    enum: EQuestionType,
    isArray: true,
    example: [EQuestionType.MULTIPLE_CHOICE, EQuestionType.TRUE_FALSE]
  })
  @IsOptional()
  @IsArray()
  @IsEnum(EQuestionType, { each: true })
  @ArrayMaxSize(10)
  type?: EQuestionType[];

  @ApiPropertyOptional({ 
    description: 'Grade level filter',
    example: 'Primary 2'
  })
  @IsOptional()
  @IsString()
  @MaxLength(20)
  grade?: string;

  @ApiPropertyOptional({ 
    description: 'Question difficulty filter',
    enum: EQuestionDifficulty,
    isArray: true
  })
  @IsOptional()
  @IsArray()
  @IsEnum(EQuestionDifficulty, { each: true })
  @ArrayMaxSize(5)
  difficulty?: EQuestionDifficulty[];

  @ApiPropertyOptional({ 
    description: 'Question status filter',
    enum: EQuestionStatus,
    default: EQuestionStatus.ACTIVE
  })
  @IsOptional()
  @IsEnum(EQuestionStatus)
  status?: EQuestionStatus;

  @ApiPropertyOptional({ 
    description: 'Language filter',
    example: 'English',
    default: 'English'
  })
  @IsOptional()
  @IsString()
  @MaxLength(50)
  language?: string;

  @ApiPropertyOptional({ 
    description: 'Search text in question content',
    example: 'capital of France'
  })
  @IsOptional()
  @IsString()
  @MaxLength(200)
  searchText?: string;

  @ApiPropertyOptional({ 
    description: 'Tags filter',
    type: [String],
    example: ['geometry', 'basic']
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  @ArrayMaxSize(10)
  tags?: string[];

  @ApiPropertyOptional({ 
    description: 'Option type ID filter',
    example: '123e4567-e89b-12d3-a456-426614174000'
  })
  @IsOptional()
  @IsString()
  optionTypeId?: string;

  @ApiPropertyOptional({ 
    description: 'Option value ID filter',
    example: '987fcdeb-51a2-43d1-b789-123456789abc'
  })
  @IsOptional()
  @IsString()
  optionValueId?: string;

  @ApiPropertyOptional({ 
    description: 'Page number for pagination',
    minimum: 1,
    default: 1
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Transform(({ value }) => parseInt(value))
  page?: number;

  @ApiPropertyOptional({ 
    description: 'Number of items per page',
    minimum: 1,
    maximum: 100,
    default: 20
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(100)
  @Transform(({ value }) => parseInt(value))
  limit?: number;

  @ApiPropertyOptional({ 
    description: 'Sort field',
    enum: ['createdAt', 'updatedAt', 'subject', 'grade', 'type'],
    default: 'createdAt'
  })
  @IsOptional()
  @IsString()
  sortBy?: 'createdAt' | 'updatedAt' | 'subject' | 'grade' | 'type';

  @ApiPropertyOptional({ 
    description: 'Sort order',
    enum: ['asc', 'desc'],
    default: 'desc'
  })
  @IsOptional()
  @IsString()
  sortOrder?: 'asc' | 'desc';

  @ApiPropertyOptional({ 
    description: 'Include questions with media only',
    default: false
  })
  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => value === 'true' || value === true)
  hasMedia?: boolean;

  @ApiPropertyOptional({ 
    description: 'Exclude questions that have been used recently (in hours)',
    minimum: 0,
    maximum: 168
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(168)
  @Transform(({ value }) => parseInt(value))
  excludeRecentlyUsedHours?: number;
}

/**
 * DTO for question pool response
 */
export class QuestionPoolResponseDto {
  @ApiProperty({ description: 'Question ID' })
  id: string;

  @ApiProperty({ description: 'Question type', enum: EQuestionType })
  type: EQuestionType;

  @ApiProperty({ description: 'Question content' })
  content: string;

  @ApiProperty({ description: 'Answer options', type: [String] })
  options: string[];

  @ApiProperty({ description: 'Correct answers', type: [String] })
  answer: string[];

  @ApiProperty({ description: 'Explanation' })
  explain: string;

  @ApiPropertyOptional({ description: 'Subject' })
  subject?: string;

  @ApiPropertyOptional({ description: 'Parent subject' })
  parentSubject?: string;

  @ApiPropertyOptional({ description: 'Child subject' })
  childSubject?: string;

  @ApiPropertyOptional({ description: 'Grade level' })
  grade?: string;

  @ApiPropertyOptional({ description: 'Question difficulty', enum: EQuestionDifficulty })
  difficulty?: EQuestionDifficulty;

  @ApiPropertyOptional({ description: 'Question status', enum: EQuestionStatus })
  status?: EQuestionStatus;

  @ApiPropertyOptional({ description: 'Language' })
  language?: string;

  @ApiPropertyOptional({ description: 'Image URL' })
  image?: string;

  @ApiPropertyOptional({ description: 'Image prompt' })
  imagePrompt?: string;

  @ApiPropertyOptional({ description: 'Tags', type: [String] })
  tags?: string[];

  @ApiPropertyOptional({ description: 'Option value reference' })
  optionValue?: Record<string, any>;

  @ApiProperty({ description: 'Creation timestamp' })
  createdAt: Date;

  @ApiProperty({ description: 'Last update timestamp' })
  updatedAt: Date;
}

/**
 * DTO for paginated question pool search results
 */
export class QuestionPoolSearchResultDto {
  @ApiProperty({ 
    description: 'Array of questions',
    type: [QuestionPoolResponseDto]
  })
  questions: QuestionPoolResponseDto[];

  @ApiProperty({ description: 'Total number of questions matching the criteria' })
  total: number;

  @ApiProperty({ description: 'Current page number' })
  page: number;

  @ApiProperty({ description: 'Number of items per page' })
  limit: number;

  @ApiProperty({ description: 'Total number of pages' })
  totalPages: number;

  @ApiProperty({ description: 'Whether there are more pages' })
  hasNextPage: boolean;

  @ApiProperty({ description: 'Whether there are previous pages' })
  hasPreviousPage: boolean;
}

/**
 * DTO for available filter options
 */
export class QuestionPoolFiltersDto {
  @ApiProperty({ 
    description: 'Available subjects',
    type: [String],
    example: ['Mathematics', 'Science', 'English']
  })
  subjects: string[];

  @ApiProperty({ 
    description: 'Available parent subjects',
    type: [String]
  })
  parentSubjects: string[];

  @ApiProperty({ 
    description: 'Available child subjects',
    type: [String]
  })
  childSubjects: string[];

  @ApiProperty({ 
    description: 'Available grades',
    type: [String],
    example: ['Primary 1', 'Primary 2', 'Secondary 1']
  })
  grades: string[];

  @ApiProperty({ 
    description: 'Available question types',
    enum: EQuestionType,
    isArray: true
  })
  types: EQuestionType[];

  @ApiProperty({ 
    description: 'Available difficulty levels',
    enum: EQuestionDifficulty,
    isArray: true
  })
  difficulties: EQuestionDifficulty[];

  @ApiProperty({ 
    description: 'Available languages',
    type: [String],
    example: ['English', 'Chinese', 'Malay']
  })
  languages: string[];

  @ApiProperty({ 
    description: 'Available tags',
    type: [String]
  })
  tags: string[];

  @ApiProperty({ description: 'Total number of questions in the pool' })
  totalQuestions: number;
}
