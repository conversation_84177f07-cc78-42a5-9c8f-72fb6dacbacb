import { 
  <PERSON>, 
  Get, 
  Query, 
  Param, 
  UseG<PERSON>s, 
  HttpStatus,
  <PERSON>gger,
  BadRequestException,
  NotFoundException
} from '@nestjs/common';
import { 
  ApiTags, 
  ApiOperation, 
  ApiResponse, 
  ApiBearerAuth,
  ApiQuery,
  <PERSON>piParam
} from '@nestjs/swagger';
import { AuthGuard } from '../auth/guards/auth.guard';
import { RoleGuard } from '../auth/guards/role.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { ActiveUser } from '../auth/decorators/active-user.decorator';
import { EUserRole } from '../user/dto/create-user.dto';
import { User } from '../user/entities/user.entity';
import { QuestionPoolService } from './question-pool.service';
import { 
  QuestionPoolSearchDto, 
  QuestionPoolResponseDto, 
  QuestionPoolSearchResultDto,
  QuestionPoolFiltersDto
} from './dto/question-pool-query.dto';
import { QuestionPool } from '../mongodb/schemas/question-pool.schema';
import { EQuestionType, EQuestionDifficulty, EQuestionStatus } from '../../shared/interfaces/exercise-question.interface';

@ApiTags('Question Pool')
@Controller('question-pool')
@UseGuards(AuthGuard, RoleGuard)
@ApiBearerAuth()
export class QuestionPoolController {
  private readonly logger = new Logger(QuestionPoolController.name);

  constructor(
    private readonly questionPoolService: QuestionPoolService
  ) {}

  @Get('search')
  @Roles(EUserRole.ADMIN, EUserRole.SCHOOL_MANAGER, EUserRole.TEACHER, EUserRole.INDEPENDENT_TEACHER)
  @ApiOperation({
    summary: 'Search questions in the question pool',
    description: `Search and filter questions from the question pool with comprehensive filtering options.
    
    **Access Control:**
    - **Admin users**: Can search all questions in the pool
    - **School Manager**: Can search questions relevant to their school context
    - **Teacher**: Can search questions relevant to their school context
    - **Independent Teacher**: Can search all public questions
    
    **Features:**
    - Full-text search in question content
    - Multiple filter options (subject, grade, type, difficulty, etc.)
    - Pagination support
    - Sorting options
    - Exclude recently used questions
    `
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Questions retrieved successfully',
    type: QuestionPoolSearchResultDto
  })
  @ApiResponse({ status: 400, description: 'Bad request - Invalid search parameters' })
  @ApiResponse({ status: 403, description: 'Forbidden - Insufficient permissions' })
  async searchQuestions(
    @Query() searchDto: QuestionPoolSearchDto,
    @ActiveUser() user: User
  ): Promise<QuestionPoolSearchResultDto> {
    try {
      this.logger.log(`Searching question pool by user ${user.id} with filters: ${JSON.stringify(searchDto)}`);

      // Set default values
      const page = searchDto.page || 1;
      const limit = searchDto.limit || 20;
      const skip = (page - 1) * limit;

      // Build filters based on user role and permissions
      const filters = this.buildSearchFilters(searchDto, user);

      // Get questions from the pool
      const result = await this.questionPoolService.getQuestions(
        filters,
        limit,
        skip
      );

      // Transform to response format
      const questions = result.questions.map(q => this.transformToResponseDto(q));

      // Calculate pagination metadata
      const totalPages = Math.ceil(result.total / limit);
      const hasNextPage = page < totalPages;
      const hasPreviousPage = page > 1;

      return {
        questions,
        total: result.total,
        page,
        limit,
        totalPages,
        hasNextPage,
        hasPreviousPage
      };

    } catch (error) {
      this.logger.error(`Error searching question pool: ${error.message}`, error.stack);
      throw error;
    }
  }

  @Get('filters')
  @Roles(EUserRole.ADMIN, EUserRole.SCHOOL_MANAGER, EUserRole.TEACHER, EUserRole.INDEPENDENT_TEACHER)
  @ApiOperation({
    summary: 'Get available filter options for question pool search',
    description: 'Returns all available filter options including subjects, grades, types, difficulties, and languages available in the question pool.'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Filter options retrieved successfully',
    type: QuestionPoolFiltersDto
  })
  async getFilterOptions(
    @ActiveUser() user: User
  ): Promise<QuestionPoolFiltersDto> {
    try {
      this.logger.log(`Getting filter options for user ${user.id}`);

      // Get all questions to extract unique values (this could be optimized with aggregation)
      const allQuestions = await this.questionPoolService.getQuestions({}, 10000, 0);

      // Extract unique values for filters
      const subjects = [...new Set(allQuestions.questions.map(q => q.subject).filter(Boolean))];
      const parentSubjects = [...new Set(allQuestions.questions.map(q => q.parentSubject).filter(Boolean))];
      const childSubjects = [...new Set(allQuestions.questions.map(q => q.childSubject).filter(Boolean))];
      const grades = [...new Set(allQuestions.questions.map(q => q.grade).filter(Boolean))];
      const languages = [...new Set(allQuestions.questions.map(q => q.language).filter(Boolean))];
      const tags = [...new Set(allQuestions.questions.flatMap(q => q.metadata?.tags || []))];

      return {
        subjects: subjects.sort(),
        parentSubjects: parentSubjects.sort(),
        childSubjects: childSubjects.sort(),
        grades: grades.sort(),
        types: Object.values(EQuestionType),
        difficulties: Object.values(EQuestionDifficulty),
        languages: languages.sort(),
        tags: tags.sort(),
        totalQuestions: allQuestions.total
      };

    } catch (error) {
      this.logger.error(`Error getting filter options: ${error.message}`, error.stack);
      throw error;
    }
  }

  @Get(':id')
  @Roles(EUserRole.ADMIN, EUserRole.SCHOOL_MANAGER, EUserRole.TEACHER, EUserRole.INDEPENDENT_TEACHER)
  @ApiOperation({
    summary: 'Get a specific question from the pool by ID',
    description: 'Retrieve detailed information about a specific question from the question pool.'
  })
  @ApiParam({ name: 'id', description: 'Question ID' })
  @ApiResponse({ 
    status: 200, 
    description: 'Question retrieved successfully',
    type: QuestionPoolResponseDto
  })
  @ApiResponse({ status: 404, description: 'Question not found' })
  @ApiResponse({ status: 403, description: 'Forbidden - Insufficient permissions' })
  async getQuestionById(
    @Param('id') id: string,
    @ActiveUser() user: User
  ): Promise<QuestionPoolResponseDto> {
    try {
      this.logger.log(`Getting question ${id} for user ${user.id}`);

      // Get the question by ID
      const question = await this.questionPoolService.getQuestionById(id);
      
      if (!question) {
        throw new NotFoundException(`Question with ID ${id} not found`);
      }

      // Check access permissions (implement based on your business rules)
      this.validateQuestionAccess(question, user);

      return this.transformToResponseDto(question);

    } catch (error) {
      this.logger.error(`Error getting question ${id}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Build search filters based on user permissions and search criteria
   */
  private buildSearchFilters(searchDto: QuestionPoolSearchDto, user: User): any {
    const filters: any = {
      status: searchDto.status || EQuestionStatus.ACTIVE,
      language: searchDto.language || 'English'
    };

    // Add optional filters
    if (searchDto.subject) filters.subject = searchDto.subject;
    if (searchDto.parentSubject) filters.parentSubject = searchDto.parentSubject;
    if (searchDto.childSubject) filters.childSubject = searchDto.childSubject;
    if (searchDto.grade) filters.grade = searchDto.grade;
    if (searchDto.type && searchDto.type.length > 0) {
      filters.type = searchDto.type.length === 1 ? searchDto.type[0] : { $in: searchDto.type };
    }
    if (searchDto.optionTypeId) filters.optionTypeId = searchDto.optionTypeId;
    if (searchDto.optionValueId) filters.optionValueId = searchDto.optionValueId;

    // Add text search if provided
    if (searchDto.searchText) {
      filters.$text = { $search: searchDto.searchText };
    }

    // Add difficulty filter
    if (searchDto.difficulty && searchDto.difficulty.length > 0) {
      filters.difficulty = searchDto.difficulty.length === 1 ? searchDto.difficulty[0] : { $in: searchDto.difficulty };
    }

    // Add media filter
    if (searchDto.hasMedia) {
      filters.$or = [
        { image: { $exists: true, $ne: null } },
        { imageUrl: { $exists: true, $ne: null } },
        { 'media.imageUrl': { $exists: true, $ne: null } }
      ];
    }

    // Add recently used exclusion
    if (searchDto.excludeRecentlyUsedHours) {
      const cutoffDate = new Date();
      cutoffDate.setHours(cutoffDate.getHours() - searchDto.excludeRecentlyUsedHours);
      filters.lastUsed = { $lt: cutoffDate };
    }

    // Apply user-specific filters based on role
    if (user.role === EUserRole.INDEPENDENT_TEACHER) {
      // Independent teachers can only see public questions
      filters.isPublic = true;
    } else if (user.role !== EUserRole.ADMIN && user.schoolId) {
      // School-based users see questions relevant to their school
      filters.$or = [
        { schoolId: user.schoolId },
        { isPublic: true },
        { schoolId: { $exists: false } }
      ];
    }

    return filters;
  }

  /**
   * Transform QuestionPool document to response DTO
   */
  private transformToResponseDto(question: QuestionPool): QuestionPoolResponseDto {
    return {
      id: question._id?.toString() || question.id,
      type: question.type as EQuestionType,
      content: question.content,
      options: question.options || [],
      answer: question.answer || [],
      explain: question.explain || '',
      subject: question.subject,
      parentSubject: question.parentSubject,
      childSubject: question.childSubject,
      grade: question.grade,
      difficulty: question.difficulty as EQuestionDifficulty,
      status: question.status as EQuestionStatus,
      language: question.language,
      image: question.image,
      imagePrompt: question.imagePrompt,
      tags: question.metadata?.tags,
      optionValue: question.optionValue,
      createdAt: question.createdAt || new Date(),
      updatedAt: question.updatedAt || new Date()
    };
  }

  /**
   * Validate user access to a specific question
   */
  private validateQuestionAccess(question: QuestionPool, user: User): void {
    // Admin can access all questions
    if (user.role === EUserRole.ADMIN) {
      return;
    }

    // Independent teachers can only access public questions
    if (user.role === EUserRole.INDEPENDENT_TEACHER && !question.isPublic) {
      throw new BadRequestException('Access denied to this question');
    }

    // School-based users can access their school's questions and public questions
    if (user.schoolId && question.schoolId && question.schoolId !== user.schoolId && !question.isPublic) {
      throw new BadRequestException('Access denied to this question');
    }
  }
}
